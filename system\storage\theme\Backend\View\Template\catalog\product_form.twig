<!-- Product Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div class="flex items-center">
			<a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
				<div class="w-8 h-8 flex items-center justify-center">
					<i class="ri-arrow-left-line ri-lg"></i>
				</div>
			</a>
			<div>
				<h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
			</div>
		</div>
		<div class="flex items-center space-x-3 mt-4 md:mt-0">
			<a href="{{ back }}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors whitespace-nowrap !rounded-button">
				<span>Отказ</span>
			</a>
			<button type="submit" form="product-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-save-line"></i>
				</div>
				<span>Запази</span>
			</button>
		</div>
	</div>
</div>
<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<form id="product-form" class="space-y-6">
		<input type="hidden" name="product_id" value="{{ product_id ?? 0 }}">
		<div class="max-w-7xl">
			<div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
				<div class="border-b border-gray-200">
					<div class="flex overflow-x-auto">
						<button data-tab="tab-basic-info" class="tab-button active px-6 py-4 text-sm font-medium whitespace-nowrap">Основна информация</button>
						<button data-tab="tab-images" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Изображения</button>
						<button data-tab="tab-description" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Описание</button>
						<button data-tab="tab-specifications" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Характеристики</button>
						<button data-tab="tab-seo" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">SEO</button>
					</div>
				</div>
				<!-- Tab Content -->
				<div
					class="p-6">
					<!-- Basic Info Tab -->
					<div id="tab-basic-info" class="tab-content">
						<!-- Езикови табове -->
						<div class="mb-6">
							<div class="border-b border-gray-200">
								<nav class="-mb-px flex space-x-8">
									{% for language in languages %}
									<button type="button" class="language-tab py-2 px-1 border-b-2 font-medium text-sm {% if language.language_id == active_language_id %}border-primary text-primary active{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %}" data-language="{{ language.language_id }}">
										<img src="language/{{ language.code }}/{{ language.code }}.png" alt="{{ language.name }}" class="inline-block w-4 h-4 mr-1">
										{{ language.name }}
									</button>
									{% endfor %}
								</nav>
							</div>
						</div>

						<!-- Многоезично съдържание -->
						{% for language in languages %}
						<div class="language-content {% if language.language_id != active_language_id %}hidden{% endif %} mb-6" data-language="{{ language.language_id }}">

							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Име на продукта ({{ language.name }})
										<span class="text-red-500">*</span>
									</label>
									<input type="text" name="product_description[{{ language.language_id }}][name]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете име на продукта" value="{{ product_description[language.language_id].name ?? '' }}">
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Тагове ({{ language.name }})</label>
									<input type="text" name="product_description[{{ language.language_id }}][tag]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете тагове, разделени със запетая" value="{{ product_description[language.language_id].tag ?? '' }}">
								</div>
							</div>
						</div>
						{% endfor %}

						<!-- Debug информация за основните полета -->
						<!-- <div class="mb-2 p-2 bg-blue-100 text-xs">
							Debug основни полета: Model: "{{ model ?? 'НЯМА' }}", Price: "{{ price ?? 'НЯМА' }}", Quantity: "{{ quantity ?? 'НЯМА' }}", Status: "{{ status ?? 'НЯМА' }}"
						</div> -->

						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Код на продукта
									<span class="text-red-500">*</span>
								</label>
								<input type="text" name="model" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете код на продукта" value="{{ model }}">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Категория
									<span class="text-red-500">*</span>
								</label>
								<input type="text" name="category_name" id="input-category" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Започнете да пишете за търсене на категория..." value="">
								<input type="hidden" name="category_id" id="category-id">
								<div id="product-category-autocomplete" class="mt-2 space-y-2">
								</div>
								<div id="product-category" class="mt-2 space-y-2">
									{% for category_id in product_category %}
									<div id="product-category-{{ category_id }}" class="flex items-center justify-between p-2 bg-gray-100 rounded">
										<span class="text-sm">{{ categories[category_id].name }}</span>
										<button type="button" class="text-gray-400 hover:text-red-500 remove-category">
											<i class="ri-close-line"></i>
										</button>
										<input type="hidden" name="product_category[]" value="{{ category_id }}">
									</div>
									{% endfor %}
								</div>
							</div>
							<div>
								<div class="mb-4">
									<label for="input-manufacturer" class="block text-sm font-medium text-gray-700 mb-1">Марка</label>
									<input
										type="text"
										name="manufacturer_name"
										id="input-manufacturer"
										class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
										placeholder="Започнете да пишете за търсене на марка..."
										value="{{ manufacturer_name|default('') }}"
										autocomplete="off"
										{% if manufacturer_id %}data-initial-name="{{ manufacturer_name }}"{% endif %}
									>
									<div id="product-manufacturer-autocomplete" class="autocomplete-suggestions-container mt-1 relative" style="z-index: 1000;">
										<!-- Предложенията ще се зареждат тук от JavaScript -->
									</div>
									<div id="selected-manufacturer-container">
										{% if manufacturer_id and manufacturer_name %}
											<div class="selected-brand-badge flex items-center justify-between bg-primary/10 text-primary px-3 py-1.5 rounded-md text-sm my-1">
												<span>{{ manufacturer_name }}</span>
												<button type="button" class="remove-selected-manufacturer ml-2 text-red-500 hover:text-red-700" data-id="{{ manufacturer_id }}">
													<i class="ri-close-line"></i>
												</button>
											</div>
										{% endif %}
									</div>
									<input type="hidden" name="manufacturer_id" value="{{ manufacturer_id|default('') }}">
								</div>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Основна цена (лв.)
									<span class="text-red-500">*</span>
								</label>
								<div class="relative">
									<input type="number" name="price" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00" value="{{ price }}">
									<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
										<span class="text-gray-500 text-sm">лв.</span>
									</div>
								</div>
							</div>
							<div class="md:col-span-2">
								<div class="mb-4 flex justify-between items-center">
									<label class="block text-sm font-medium text-gray-700">Промоционални цени</label>
									<button type="button" id="add-special-price" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
										<div class="w-4 h-4 flex items-center justify-center mr-1">
											<i class="ri-add-line"></i>
										</div>
										<span>Добави промоция</span>
									</button>
								</div>
								<div id="special-prices-container" class="space-y-4">
									{% if product_special is not empty %}
										{% for special in product_special %}
										<div class="special-price-item grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border border-gray-200 rounded-lg">
											<div>
												<label class="block text-sm font-medium text-gray-700 mb-1">Цена (лв.)</label>
												<div class="relative">
													<input type="number" name="product_special[{{ loop.index0 }}][price]" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00" value="{{ special.price ?? '' }}">
													<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
														<span class="text-gray-500 text-sm">лв.</span>
													</div>
												</div>
											</div>
											<div>
												<label class="block text-sm font-medium text-gray-700 mb-1">Дата от</label>
												<input type="date" name="product_special[{{ loop.index0 }}][date_start]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" value="{{ special.date_start ?? '' }}">
											</div>
											<div>
												<label class="block text-sm font-medium text-gray-700 mb-1">Дата до</label>
												<input type="date" name="product_special[{{ loop.index0 }}][date_end]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" value="{{ special.date_end ?? '' }}">
											</div>
											<div class="flex items-end">
												<button type="button" class="remove-special-price w-full px-3 py-2 bg-red-50 text-red-500 border border-gray-300 rounded hover:bg-red-100 transition-colors">
													<i class="ri-delete-bin-line"></i>
												</button>
											</div>
										</div>
										{% endfor %}
									{% else %}
										<!-- Празен контейнер когато няма промоционални цени -->
										<div class="text-gray-500 text-sm italic">
											Няма добавени промоционални цени. Използвайте бутона "Добави промоция" за да добавите нова промоционална цена.
										</div>
									{% endif %}
								</div>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Наличност
									<span class="text-red-500">*</span>
								</label>
								<input type="number" name="quantity" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете количество" value="{{ quantity }}">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">SKU</label>
								<input type="text" name="sku" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете SKU" value="{{ sku }}">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Минимално количество</label>
								<input type="number" name="minimum" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="1" value="{{ minimum }}">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
								<div class="flex items-center space-x-2 mt-2">
									<label class="toggle-switch">
										<input type="checkbox" name="status" value="1" {% if status %}checked{% endif %}>
										<span class="toggle-slider"></span>
									</label>
									<span class="text-sm text-gray-700">Активен</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Images Tab (Hidden by default) -->
					<div id="tab-images" class="tab-content hidden">
						<div class="mb-6">
							<label class="block text-sm font-medium text-gray-700 mb-2">Изображения на продукта</label>
							<div class="image-upload-area rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer">
								<div class="w-16 h-16 flex items-center justify-center text-gray-400 mb-4">
									<i class="ri-image-add-line ri-2x"></i>
								</div>
								<p class="text-sm text-gray-500 mb-1">Плъзнете и пуснете изображения тук</p>
								<p class="text-xs text-gray-400">или</p>
								<label class="mt-4 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors cursor-pointer !rounded-button">
									<span>Изберете файлове</span>
									<input type="file" name="image" multiple class="hidden" accept="image/*">
								</label>
								<p class="text-xs text-gray-400 mt-4">Поддържани формати: JPG, PNG, GIF. Максимален размер: 5MB</p>
							</div>
						</div>
						<div class="mt-8">
							<h3 class="text-sm font-medium text-gray-700 mb-4">Качени изображения</h3>
							<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
								<div class="relative group">
									<div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
										<img src="{{ thumb }}" alt="Product image" class="w-full h-full object-cover">
									</div>
									<div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
										<button class="p-2 bg-white rounded-full text-gray-700 hover:text-primary">
											<div class="w-5 h-5 flex items-center justify-center">
												<i class="ri-eye-line"></i>
											</div>
										</button>
										<button class="p-2 bg-white rounded-full text-gray-700 hover:text-primary">
											<div class="w-5 h-5 flex items-center justify-center">
												<i class="ri-image-edit-line"></i>
											</div>
										</button>
										<button class="p-2 bg-white rounded-full text-red-500 hover:text-red-600">
											<div class="w-5 h-5 flex items-center justify-center">
												<i class="ri-delete-bin-line"></i>
											</div>
										</button>
									</div>
									<div class="absolute top-2 left-2">
										<div class="px-2 py-1 bg-primary text-white text-xs rounded-full">Основна</div>
									</div>
								</div>
								<div class="border border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" onclick="document.getElementById('additional-image-upload').click()">
									<div class="w-8 h-8 flex items-center justify-center text-gray-400">
										<i class="ri-add-line ri-lg"></i>
									</div>
									<input type="file" name="product_image[][image]" id="additional-image-upload" class="hidden" accept="image/*" multiple>
								</div>
							</div>
						</div>
					</div>
					<!-- Description Tab (Hidden by default) -->
					<div id="tab-description" class="tab-content hidden">
						<!-- Езикови табове за описанието -->
						<div class="mb-6">
							<div class="border-b border-gray-200">
								<nav class="-mb-px flex space-x-8">
									{% for language in languages %}
									<button type="button" class="description-language-tab py-2 px-1 border-b-2 font-medium text-sm {% if language.language_id == active_language_id %}border-primary text-primary active{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %}" data-language="{{ language.language_id }}">
										<img src="language/{{ language.code }}/{{ language.code }}.png" alt="{{ language.name }}" class="inline-block w-4 h-4 mr-1">
										{{ language.name }}
									</button>
									{% endfor %}
								</nav>
							</div>
						</div>

						{% for language in languages %}
						<div class="description-language-content {% if language.language_id != active_language_id %}hidden{% endif %}" data-language="{{ language.language_id }}">
							<div class="mt-6">
								<label class="block text-sm font-medium text-gray-700 mb-2">Пълно описание ({{ language.name }})</label>
								<div class="border border-gray-300 rounded overflow-hidden">
									<div class="bg-gray-50 border-b border-gray-300 p-2 flex space-x-2">
										<button type="button" class="p-1 hover:bg-gray-200 rounded">
											<div class="w-6 h-6 flex items-center justify-center">
												<i class="ri-bold"></i>
											</div>
										</button>
										<button type="button" class="p-1 hover:bg-gray-200 rounded">
											<div class="w-6 h-6 flex items-center justify-center">
												<i class="ri-italic"></i>
											</div>
										</button>
										<button type="button" class="p-1 hover:bg-gray-200 rounded">
											<div class="w-6 h-6 flex items-center justify-center">
												<i class="ri-underline"></i>
											</div>
										</button>
										<button type="button" class="p-1 hover:bg-gray-200 rounded">
											<div class="w-6 h-6 flex items-center justify-center">
												<i class="ri-list-unordered"></i>
											</div>
										</button>
										<button type="button" class="p-1 hover:bg-gray-200 rounded">
											<div class="w-6 h-6 flex items-center justify-center">
												<i class="ri-list-ordered"></i>
											</div>
										</button>
										<button type="button" class="p-1 hover:bg-gray-200 rounded">
											<div class="w-6 h-6 flex items-center justify-center">
												<i class="ri-link"></i>
											</div>
										</button>
										<button type="button" class="p-1 hover:bg-gray-200 rounded">
											<div class="w-6 h-6 flex items-center justify-center">
												<i class="ri-image-line"></i>
											</div>
										</button>
									</div>
									<textarea name="product_description[{{ language.language_id }}][description]" rows="10" class="w-full px-3 py-2 border-none focus:outline-none focus:ring-0 text-sm" placeholder="Въведете пълно описание на продукта">{{ product_description[language.language_id].description ?? '' }}</textarea>
								</div>
							</div>
						</div>
						{% endfor %}
					</div>
					<!-- Specifications Tab (Hidden by default) -->
					<div id="tab-specifications" class="tab-content hidden">
						<div class="mb-4 flex justify-between items-center">
							<h3 class="text-sm font-medium text-gray-700">Характеристики на продукта</h3>
							<button id="add-specification" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
								<div class="w-4 h-4 flex items-center justify-center mr-1">
									<i class="ri-add-line"></i>
								</div>
								<span>Добави</span>
							</button>
						</div>
						<div id="specifications-container" class="space-y-4">
							<div class="specification-item grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg">
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
									<input type="text" name="product_attribute[0][name]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Цвят, Размер, Материал" value="{{ product_attribute[0].name ?? '' }}">
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
									<div class="flex">
										<input type="text" name="product_attribute[0][product_attribute_description][2][text]" class="flex-1 px-3 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Напр. Червен, XL, Памук" value="{{ product_attribute[0].product_attribute_description[2].text ?? '' }}">
										<button class="remove-specification px-3 py-2 bg-red-50 text-red-500 border border-gray-300 border-l-0 rounded-r hover:bg-red-100 transition-colors">
											<div class="w-5 h-5 flex items-center justify-center">
												<i class="ri-delete-bin-line"></i>
											</div>
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- SEO Tab (Hidden by default) -->
					<div id="tab-seo" class="tab-content hidden">
						<!-- Езикови табове за SEO -->
						<div class="mb-6">
							<div class="border-b border-gray-200">
								<nav class="-mb-px flex space-x-8">
									{% for language in languages %}
									<button type="button" class="seo-language-tab py-2 px-1 border-b-2 font-medium text-sm {% if language.language_id == active_language_id %}border-primary text-primary active{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %}" data-language="{{ language.language_id }}">
										<img src="language/{{ language.code }}/{{ language.code }}.png" alt="{{ language.name }}" class="inline-block w-4 h-4 mr-1">
										{{ language.name }}
									</button>
									{% endfor %}
								</nav>
							</div>
						</div>

						{% for language in languages %}
						<div class="seo-language-content {% if language.language_id != active_language_id %}hidden{% endif %}" data-language="{{ language.language_id }}">
							<div class="space-y-6">
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">URL Slug ({{ language.name }})</label>
									<div class="flex">
										<span class="inline-flex items-center px-3 text-gray-500 bg-gray-100 border border-r-0 border-gray-300 rounded-l text-sm">{{ site_url }}</span>
										<input type="text" name="product_seo_url[0][{{ language.language_id }}][keyword]" class="flex-1 px-3 py-2 border border-gray-300 rounded-r focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="product-name" value="{{ product_seo_url[0][language.language_id].keyword ?? '' }}">
									</div>
									<p class="mt-1 text-xs text-gray-500">URL адресът ще бъде генериран автоматично от името на продукта, но можете да го промените.</p>
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Meta заглавие ({{ language.name }})</label>
									<input type="text" name="product_description[{{ language.language_id }}][meta_title]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете мета заглавие" value="{{ product_description[language.language_id].meta_title ?? '' }}">
									<p class="mt-1 text-xs text-gray-500">Препоръчителна дължина: 50-60 символа</p>
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Meta описание ({{ language.name }})</label>
									<textarea name="product_description[{{ language.language_id }}][meta_description]" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете мета описание">{{ product_description[language.language_id].meta_description ?? '' }}</textarea>
									<p class="mt-1 text-xs text-gray-500">Препоръчителна дължина: 150-160 символа</p>
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Ключови думи ({{ language.name }})</label>
									<input type="text" name="product_description[{{ language.language_id }}][meta_keyword]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете ключови думи, разделени със запетая" value="{{ product_description[language.language_id].meta_keyword ?? '' }}">
									<p class="mt-1 text-xs text-gray-500">Пример: смартфон, телефон, мобилен телефон</p>
								</div>
							</div>
						</div>
						{% endfor %}
					</div>
				</div>
			</div>
		</div>
	</form>
</main>
