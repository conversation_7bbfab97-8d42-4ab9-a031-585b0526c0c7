<?php
class ModelCatalogProduct extends Model {
    
    /**
     * Получаване на продукт по ID - връща всички данни без ограничение по език
     */
    public function getProduct($product_id) {
        $query = $this->db->query("SELECT DISTINCT * FROM " . DB_PREFIX . "product WHERE product_id = '" . (int)$product_id . "'");
        
        return $query->row;
    }
    
    /**
     * Получаване на описанията на продукт за всички езици
     */
    public function getProductDescriptions($product_id) {
        $product_description_data = array();

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "product_description WHERE product_id = '" . (int)$product_id . "'");

        foreach ($query->rows as $result) {
            $product_description_data[$result['language_id']] = array(
                'name'             => $result['name'],
                'description'      => $result['description'],
                'meta_title'       => $result['meta_title'],
                'meta_description' => $result['meta_description'],
                'meta_keyword'     => $result['meta_keyword'],
                'tag'              => $result['tag']
            );
        }

        return $product_description_data;
    }
    
    /**
     * Получаване на категориите на продукт
     */
    public function getProductCategories($product_id) {
        $product_category_data = array();

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "product_to_category WHERE product_id = '" . (int)$product_id . "'");

        foreach ($query->rows as $result) {
            $product_category_data[] = $result['category_id'];
        }

        return $product_category_data;
    }
    
    /**
     * Получаване на промоционалните цени на продукт
     */
    public function getProductSpecials($product_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "product_special WHERE product_id = '" . (int)$product_id . "' ORDER BY priority, price");

        return $query->rows;
    }
    
    /**
     * Получаване на изображенията на продукт
     */
    public function getProductImages($product_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "product_image WHERE product_id = '" . (int)$product_id . "' ORDER BY sort_order ASC");

        return $query->rows;
    }
    
    /**
     * Получаване на атрибутите на продукт
     */
    public function getProductAttributes($product_id) {
        $product_attribute_data = array();

        $product_attribute_query = $this->db->query("SELECT attribute_id FROM " . DB_PREFIX . "product_attribute WHERE product_id = '" . (int)$product_id . "' GROUP BY attribute_id");

        foreach ($product_attribute_query->rows as $product_attribute) {
            $product_attribute_description_data = array();

            $product_attribute_description_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "product_attribute WHERE product_id = '" . (int)$product_id . "' AND attribute_id = '" . (int)$product_attribute['attribute_id'] . "'");

            foreach ($product_attribute_description_query->rows as $product_attribute_description) {
                $product_attribute_description_data[$product_attribute_description['language_id']] = array('text' => $product_attribute_description['text']);
            }

            $product_attribute_data[] = array(
                'attribute_id'                  => $product_attribute['attribute_id'],
                'product_attribute_description' => $product_attribute_description_data
            );
        }

        return $product_attribute_data;
    }
    
    /**
     * Получаване на SEO URL-тата на продукт
     */
    public function getProductSeoUrls($product_id) {
        $product_seo_url_data = array();

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "seo_url WHERE query = 'product_id=" . (int)$product_id . "'");

        foreach ($query->rows as $result) {
            $product_seo_url_data[$result['store_id']][$result['language_id']] = $result['keyword'];
        }

        return $product_seo_url_data;
    }
    
    /**
     * Получаване на свързаните продукти
     */
    public function getProductRelated($product_id) {
        $product_related_data = array();

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "product_related WHERE product_id = '" . (int)$product_id . "'");

        foreach ($query->rows as $result) {
            $product_related_data[] = $result['related_id'];
        }

        return $product_related_data;
    }
    
    /**
     * Добавяне на продукт
     */
    public function addProduct($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "product SET 
            model = '" . $this->db->escape($data['model']) . "', 
            sku = '" . $this->db->escape($data['sku']) . "', 
            upc = '" . $this->db->escape($data['upc']) . "', 
            ean = '" . $this->db->escape($data['ean']) . "', 
            jan = '" . $this->db->escape($data['jan']) . "', 
            isbn = '" . $this->db->escape($data['isbn']) . "', 
            mpn = '" . $this->db->escape($data['mpn']) . "', 
            location = '" . $this->db->escape($data['location']) . "', 
            quantity = '" . (int)$data['quantity'] . "', 
            minimum = '" . (int)$data['minimum'] . "', 
            subtract = '" . (int)$data['subtract'] . "', 
            stock_status_id = '" . (int)$data['stock_status_id'] . "', 
            date_available = '" . $this->db->escape($data['date_available']) . "', 
            manufacturer_id = '" . (int)$data['manufacturer_id'] . "', 
            shipping = '" . (int)$data['shipping'] . "', 
            price = '" . (float)$data['price'] . "', 
            points = '" . (int)($data['points'] ?? 0) . "', 
            weight = '" . (float)$data['weight'] . "', 
            weight_class_id = '" . (int)$data['weight_class_id'] . "', 
            length = '" . (float)$data['length'] . "', 
            width = '" . (float)$data['width'] . "', 
            height = '" . (float)$data['height'] . "', 
            length_class_id = '" . (int)$data['length_class_id'] . "', 
            status = '" . (int)$data['status'] . "', 
            tax_class_id = '" . (int)$data['tax_class_id'] . "', 
            sort_order = '" . (int)$data['sort_order'] . "', 
            date_added = NOW()");

        $product_id = $this->db->getLastId();

        if (isset($data['image'])) {
            $this->db->query("UPDATE " . DB_PREFIX . "product SET image = '" . $this->db->escape($data['image']) . "' WHERE product_id = '" . (int)$product_id . "'");
        }

        foreach ($data['product_description'] as $language_id => $value) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "product_description SET 
                product_id = '" . (int)$product_id . "', 
                language_id = '" . (int)$language_id . "', 
                name = '" . $this->db->escape($value['name']) . "', 
                description = '" . $this->db->escape($value['description']) . "', 
                tag = '" . $this->db->escape($value['tag']) . "', 
                meta_title = '" . $this->db->escape($value['meta_title']) . "', 
                meta_description = '" . $this->db->escape($value['meta_description']) . "', 
                meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "'");
        }

        if (isset($data['product_category'])) {
            foreach ($data['product_category'] as $category_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_category SET product_id = '" . (int)$product_id . "', category_id = '" . (int)$category_id . "'");
            }
        }

        if (isset($data['product_special'])) {
            foreach ($data['product_special'] as $product_special) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_special SET 
                    product_id = '" . (int)$product_id . "', 
                    customer_group_id = '" . (int)($product_special['customer_group_id'] ?? 0) . "', 
                    priority = '" . (int)($product_special['priority'] ?? 1) . "', 
                    price = '" . (float)$product_special['price'] . "', 
                    date_start = '" . $this->db->escape($product_special['date_start']) . "', 
                    date_end = '" . $this->db->escape($product_special['date_end']) . "'");
            }
        }

        if (isset($data['product_image'])) {
            foreach ($data['product_image'] as $product_image) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_image SET 
                    product_id = '" . (int)$product_id . "', 
                    image = '" . $this->db->escape($product_image['image']) . "', 
                    sort_order = '" . (int)$product_image['sort_order'] . "'");
            }
        }

        if (isset($data['product_related'])) {
            foreach ($data['product_related'] as $related_id) {
                $this->db->query("DELETE FROM " . DB_PREFIX . "product_related WHERE product_id = '" . (int)$product_id . "' AND related_id = '" . (int)$related_id . "'");
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_related SET product_id = '" . (int)$product_id . "', related_id = '" . (int)$related_id . "'");
                $this->db->query("DELETE FROM " . DB_PREFIX . "product_related WHERE product_id = '" . (int)$related_id . "' AND related_id = '" . (int)$product_id . "'");
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_related SET product_id = '" . (int)$related_id . "', related_id = '" . (int)$product_id . "'");
            }
        }

        // SEO URL
        if (isset($data['product_seo_url'])) {
            foreach ($data['product_seo_url'] as $store_id => $language) {
                foreach ($language as $language_id => $keyword) {
                    if (trim($keyword)) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "seo_url SET 
                            store_id = '" . (int)$store_id . "', 
                            language_id = '" . (int)$language_id . "', 
                            query = 'product_id=" . (int)$product_id . "', 
                            keyword = '" . $this->db->escape($keyword) . "'");
                    }
                }
            }
        }

        $this->cache->delete('product');

        return $product_id;
    }

    /**
     * Редактиране на продукт
     */
    public function editProduct($product_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "product SET
            model = '" . $this->db->escape($data['model']) . "',
            sku = '" . $this->db->escape($data['sku']) . "',
            upc = '" . $this->db->escape($data['upc']) . "',
            ean = '" . $this->db->escape($data['ean']) . "',
            jan = '" . $this->db->escape($data['jan']) . "',
            isbn = '" . $this->db->escape($data['isbn']) . "',
            mpn = '" . $this->db->escape($data['mpn']) . "',
            location = '" . $this->db->escape($data['location']) . "',
            quantity = '" . (int)$data['quantity'] . "',
            minimum = '" . (int)$data['minimum'] . "',
            subtract = '" . (int)$data['subtract'] . "',
            stock_status_id = '" . (int)$data['stock_status_id'] . "',
            date_available = '" . $this->db->escape($data['date_available']) . "',
            manufacturer_id = '" . (int)$data['manufacturer_id'] . "',
            shipping = '" . (int)$data['shipping'] . "',
            price = '" . (float)$data['price'] . "',
            points = '" . (int)($data['points'] ?? 0) . "',
            weight = '" . (float)$data['weight'] . "',
            weight_class_id = '" . (int)$data['weight_class_id'] . "',
            length = '" . (float)$data['length'] . "',
            width = '" . (float)$data['width'] . "',
            height = '" . (float)$data['height'] . "',
            length_class_id = '" . (int)$data['length_class_id'] . "',
            status = '" . (int)$data['status'] . "',
            tax_class_id = '" . (int)$data['tax_class_id'] . "',
            sort_order = '" . (int)$data['sort_order'] . "',
            date_modified = NOW()
            WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['image'])) {
            $this->db->query("UPDATE " . DB_PREFIX . "product SET image = '" . $this->db->escape($data['image']) . "' WHERE product_id = '" . (int)$product_id . "'");
        }

        $this->db->query("DELETE FROM " . DB_PREFIX . "product_description WHERE product_id = '" . (int)$product_id . "'");

        foreach ($data['product_description'] as $language_id => $value) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "product_description SET
                product_id = '" . (int)$product_id . "',
                language_id = '" . (int)$language_id . "',
                name = '" . $this->db->escape($value['name']) . "',
                description = '" . $this->db->escape($value['description']) . "',
                tag = '" . $this->db->escape($value['tag']) . "',
                meta_title = '" . $this->db->escape($value['meta_title']) . "',
                meta_description = '" . $this->db->escape($value['meta_description']) . "',
                meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "'");
        }

        $this->db->query("DELETE FROM " . DB_PREFIX . "product_to_category WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_category'])) {
            foreach ($data['product_category'] as $category_id) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_category SET product_id = '" . (int)$product_id . "', category_id = '" . (int)$category_id . "'");
            }
        }

        $this->db->query("DELETE FROM " . DB_PREFIX . "product_special WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_special'])) {
            foreach ($data['product_special'] as $product_special) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "product_special SET
                    product_id = '" . (int)$product_id . "',
                    customer_group_id = '" . (int)($product_special['customer_group_id'] ?? 0) . "',
                    priority = '" . (int)($product_special['priority'] ?? 1) . "',
                    price = '" . (float)$product_special['price'] . "',
                    date_start = '" . $this->db->escape($product_special['date_start']) . "',
                    date_end = '" . $this->db->escape($product_special['date_end']) . "'");
            }
        }

        $this->cache->delete('product');
    }
}
