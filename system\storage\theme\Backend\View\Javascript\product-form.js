/**
 * JavaScript функционалност за страницата за редактиране на продукти
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за продуктовата форма
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initProductForm();
        }
    });

    // Добавяне на функционалност за продуктовата форма към основния модул
    if (typeof BackendModule !== 'undefined') {
        // Ensure config object exists
        BackendModule.config = BackendModule.config || {};

        // Extract user_token from URL and store it
        try {
            const params = new URLSearchParams(window.location.search);
            BackendModule.config.userToken = params.get('user_token') || ''; // Default to empty string if not found
        } catch (e) {
            console.error('Error parsing URL params for user_token:', e);
            BackendModule.config.userToken = ''; // Fallback in case of error
        }

        // Добавяне на методи към основния модул
        Object.assign(BackendModule, {
            /**
             * Инициализация на функционалността за продуктовата форма
             */
            initProductForm: function() {
                this.initTabs();
                this.initImageUpload();
                this.initAdditionalImages();
                this.initCategoryAutocomplete();
                this.initBrandAutocomplete();
                this.initSpecifications();
            },

            /**
             * Инициализация на табовете
             */
            initTabs: function() {
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');
                
                // Активиране на таб от URL хеша, ако е наличен
                if (window.location.hash) {
                    const tabId = window.location.hash.substring(1);
                    const tabButton = document.querySelector(`[data-tab="${tabId}"]`);
                    if (tabButton) {
                        tabButtons.forEach(btn => btn.classList.remove('active', 'text-primary', 'border-b-2', 'border-primary') || btn.classList.add('text-gray-500'));
                        tabButton.classList.add('active', 'text-primary', 'border-b-2', 'border-primary');
                        tabButton.classList.remove('text-gray-500');
                        
                        tabContents.forEach(content => content.classList.add('hidden'));
                        const activeTab = document.getElementById(tabId);
                        if (activeTab) {
                            activeTab.classList.remove('hidden');
                        }
                    }
                }
                
                // Превключване между табовете
                tabButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const tabId = this.getAttribute('data-tab');
                        
                        // Активиране на бутона
                        tabButtons.forEach(btn => {
                            btn.classList.remove('active', 'text-primary', 'border-b-2', 'border-primary');
                            btn.classList.add('text-gray-500');
                        });
                        this.classList.add('active', 'text-primary', 'border-b-2', 'border-primary');
                        this.classList.remove('text-gray-500');
                        
                        // Показване на съдържанието
                        tabContents.forEach(content => content.classList.add('hidden'));
                        document.getElementById(tabId).classList.remove('hidden');
                        
                        // Обновяване на URL хеша
                        window.location.hash = tabId;
                    });
                });
            },

            /**
             * Инициализация на качването на основно изображение
             */
            initImageUpload: function() {
                const buttonUpload = document.getElementById('button-upload');
                
                if (buttonUpload) {
                    buttonUpload.addEventListener('click', function() {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';
                        
                        input.onchange = function(e) {
                            const file = e.target.files[0];
                            if (file) {
                                const formData = new FormData();
                                formData.append('file', file);
                                
                                fetch('/admin/common/filemanager/upload', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        document.getElementById('image-preview').src = data.thumb;
                                        document.getElementById('image').value = data.filename;
                                        BackendModule.showAlert('success', 'Изображението е качено успешно!');
                                    } else {
                                        BackendModule.showAlert('error', data.error || 'Грешка при качване на изображение');
                                    }
                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    BackendModule.showAlert('error', 'Възникна грешка при качване на изображението');
                                });
                            }
                        };
                        
                        input.click();
                    });
                }
            },

            /**
             * Инициализация на функционалността за допълнителни изображения
             */
            initAdditionalImages: function() {
                const dropZone = document.getElementById('drop-zone');
                const browseFiles = document.getElementById('browse-files');
                const addMoreImagesBtn = document.getElementById('add-more-images');
                const imagesContainer = document.getElementById('additional-images-container');
                
                if (dropZone && browseFiles) {
                    // Функция за избор на файлове
                    const selectFiles = () => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';
                        input.multiple = true;
                        input.click();
                        
                        input.onchange = (e) => {
                            const files = Array.from(e.target.files);
                            this.uploadImages(files);
                        };
                    };
                    
                    // Събитие за клик върху бутона за избор на файлове
                    browseFiles.addEventListener('click', (e) => {
                        e.preventDefault();
                        selectFiles();
                    });
                    
                    // Събитие за клик върху бутона за добавяне на още изображения
                    if (addMoreImagesBtn) {
                        addMoreImagesBtn.addEventListener('click', (e) => {
                            e.preventDefault();
                            selectFiles();
                        });
                    }
                    
                    // Събития за плъзгане и пускане
                    dropZone.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        dropZone.classList.add('border-primary');
                    });
                    
                    dropZone.addEventListener('dragleave', (e) => {
                        e.preventDefault();
                        dropZone.classList.remove('border-primary');
                    });
                    
                    dropZone.addEventListener('drop', (e) => {
                        e.preventDefault();
                        dropZone.classList.remove('border-primary');
                        
                        const files = Array.from(e.dataTransfer.files);
                        if (files.length > 0) {
                            this.uploadImages(files);
                        }
                    });
                }
                
                // Обработка на събития за редактиране/изтриване на изображения
                document.addEventListener('click', function(e) {
                    if (e.target.closest('[data-action="edit"]')) {
                        const imageItem = e.target.closest('.group');
                        if (imageItem) {
                            const img = imageItem.querySelector('img');
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            
                            input.onchange = function(event) {
                                const file = event.target.files[0];
                                if (file) {
                                    const formData = new FormData();
                                    formData.append('file', file);
                                    
                                    fetch('/admin/common/filemanager/upload', {
                                        method: 'POST',
                                        body: formData
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            img.src = data.thumb;
                                            const hiddenInput = imageItem.querySelector('input[name="product_image[]"]');
                                            if (hiddenInput) {
                                                hiddenInput.value = data.filename;
                                            }
                                            BackendModule.showAlert('success', 'Изображението е обновено успешно!');
                                        } else {
                                            BackendModule.showAlert('error', data.error || 'Грешка при качване на изображение');
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error:', error);
                                        BackendModule.showAlert('error', 'Възникна грешка при качване на изображението');
                                    });
                                }
                            };
                            
                            input.click();
                        }
                    } else if (e.target.closest('[data-action="remove"]')) {
                        const imageItem = e.target.closest('.group');
                        if (imageItem && confirm('Сигурни ли сте, че искате да изтриете това изображение?')) {
                            imageItem.remove();
                            BackendModule.showAlert('success', 'Изображението е премахнато!');
                        }
                    }
                });
            },

            /**
             * Качване на множество изображения
             * @param {Array} files Списък с файлове за качване
             */
            uploadImages: function(files) {
                if (!files || files.length === 0) return;
                
                const imagesContainer = document.getElementById('additional-images-container');
                if (!imagesContainer) return;
                
                // Проверка за валидни файлове и ограничение на размера
                const validFiles = files.filter(file => {
                    const isImage = /image\/(jpeg|png|gif)/i.test(file.type);
                    const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
                    if (!isImage) {
                        this.showAlert('error', `${file.name} не е валиден формат на изображение`);
                    }
                    if (!isValidSize) {
                        this.showAlert('error', `${file.name} е по-голям от 5MB`);
                    }
                    return isImage && isValidSize;
                });
                
                if (validFiles.length === 0) return;
                
                // Показване на индикатор за зареждане
                this.showAlert('info', 'Качване на изображения...', 0);
                
                // Качване на всеки файл последователно
                const uploadPromises = validFiles.map(file => {
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    return fetch('/admin/common/filemanager/upload', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Създаване на нов елемент за изображението
                            const nextSortOrder = document.querySelectorAll('.group').length + 1;
                            const imageHtml = `
                                <div class="relative group">
                                    <div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
                                        <img src="${data.thumb}" alt="" class="w-full h-full object-cover">
                                        <input type="hidden" name="product_image[]" value="${data.filename}">
                                    </div>
                                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" data-action="edit">
                                            <i class="ri-edit-line"></i>
                                        </button>
                                        <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-700" data-action="remove">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                    <div class="mt-2">
                                        <input type="number" name="product_image_sort_order[]" value="${nextSortOrder}" 
                                               class="w-full px-2 py-1 text-sm border border-gray-300 rounded" 
                                               placeholder="Подредба">
                                    </div>
                                </div>
                            `;
                            
                            // Добавяне на елемента към контейнера
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = imageHtml.trim();
                            imagesContainer.appendChild(tempDiv.firstChild);
                            return true;
                        } else {
                            this.showAlert('error', data.error || `Грешка при качване на ${file.name}`);
                            return false;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.showAlert('error', `Възникна грешка при качване на ${file.name}`);
                        return false;
                    });
                });
                
                // След като всички файлове са качени
                Promise.all(uploadPromises)
                    .then(results => {
                        const successCount = results.filter(Boolean).length;
                        if (successCount > 0) {
                            this.showAlert('success', `Успешно качени ${successCount} изображения`);
                        }
                    });
            }, // End of uploadImages

            /**
             * Инициализация на автозавършването за категории
             */
            initCategoryAutocomplete: function() {
                // Loading indicator за autocomplete
                let loadingIndicator = document.createElement('div');
                const categoryInput = document.getElementById('input-category');
                const categorySuggestions = document.getElementById('product-category-autocomplete');
                const categoryList = document.getElementById('product-category');

                if (!categoryInput || !categorySuggestions || !categoryList) {
                    // console.warn('Елементи за автодовършване на категории не са намерени.');
                    return;
                }

                loadingIndicator.className = 'autocomplete-loading';
                loadingIndicator.textContent = 'Зареждане...';
                loadingIndicator.style.display = 'none';
                categorySuggestions.parentNode.insertBefore(loadingIndicator, categorySuggestions);

                let categoryDebounceTimer;
                let currentCategoryRequest = null;

                const addCategoryToList = (categoryName, categoryId) => {
                    if (document.getElementById(`product-category-${categoryId}`)) {
                        categoryInput.value = '';
                        categorySuggestions.innerHTML = '';
                        categorySuggestions.classList.add('hidden');
                        loadingIndicator.style.display = 'none';
                        return;
                    }

                    const categoryElement = document.createElement('div');
                    categoryElement.id = `product-category-${categoryId}`;
                    categoryElement.className = 'flex items-center justify-between p-2 bg-gray-100 rounded';
                    categoryElement.innerHTML = `
                        <span class="text-sm">${categoryName}</span>
                        <button type="button" class="text-gray-400 hover:text-red-500 remove-category">
                            <i class="ri-close-line"></i>
                        </button>
                        <input type="hidden" name="product_category[]" value="${categoryId}">
                    `;
                    
                    const removeButton = categoryElement.querySelector('.remove-category');
                    removeButton.addEventListener('click', () => {
                        categoryElement.remove();
                    });
                    
                    categoryList.appendChild(categoryElement);
                    categoryInput.value = '';
                    categorySuggestions.innerHTML = '';
                    categorySuggestions.classList.add('hidden');
                    loadingIndicator.style.display = 'none';
                };

                const fetchAndDisplayCategorySuggestions = (currentQuery) => {
                    loadingIndicator.style.display = 'block';
                    categorySuggestions.classList.add('hidden');

                    if (currentCategoryRequest) {
                        currentCategoryRequest.abort();
                    }
                    const controller = new AbortController();
                    currentCategoryRequest = controller;

                    const urlParams = new URLSearchParams(window.location.search);
                    const userToken = urlParams.get('user_token');
                    const timestamp = new Date().getTime();

                    fetch(`index.php?route=catalog/product/autocomplete&type=category&filter_name=${encodeURIComponent(currentQuery)}&user_token=${userToken}&_=${timestamp}`, {
                        signal: controller.signal,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Грешка при зареждане на категории');
                        return response.json();
                    })
                    .then(data => {
                        loadingIndicator.style.display = 'none';
                        categorySuggestions.innerHTML = '<div class="autocomplete-suggestions"></div>';
                        const suggestionsList = categorySuggestions.querySelector('.autocomplete-suggestions');

                        if (data.length > 0) {
                            data.forEach(category => {
                                const item = document.createElement('div');
                                item.className = 'autocomplete-suggestion';
                                item.textContent = category.name;
                                item.dataset.id = category.id;
                                item.addEventListener('click', () => {
                                    addCategoryToList(category.name, category.id);
                                });
                                suggestionsList.appendChild(item);
                            });
                            categorySuggestions.classList.remove('hidden');
                        } else {
                            const noResults = document.createElement('div');
                            noResults.className = 'autocomplete-no-results';
                            noResults.textContent = currentQuery ? 'Няма намерени категории' : 'Няма предложения за категории';
                            suggestionsList.appendChild(noResults);
                            categorySuggestions.classList.remove('hidden');
                        }
                    })
                    .catch(error => {
                        loadingIndicator.style.display = 'none';
                        if (error.name !== 'AbortError') {
                            console.error('Грешка при търсене на категории:', error);
                            const suggestionsList = categorySuggestions.querySelector('.autocomplete-suggestions') || categorySuggestions;
                            suggestionsList.innerHTML = '<div class="autocomplete-no-results">Грешка при зареждане.</div>';
                            categorySuggestions.classList.remove('hidden');
                        }
                    });
                };

                categoryInput.addEventListener('input', () => {
                    clearTimeout(categoryDebounceTimer);
                    const query = categoryInput.value.trim();

                    if (query.length === 0) {
                        loadingIndicator.style.display = 'none';
                        categorySuggestions.innerHTML = '';
                        categorySuggestions.classList.add('hidden');
                        if (currentCategoryRequest) {
                            currentCategoryRequest.abort();
                        }
                        return;
                    }
                    
                    categoryDebounceTimer = setTimeout(() => {
                        fetchAndDisplayCategorySuggestions(query);
                    }, 500);
                });

                categoryInput.addEventListener('focus', () => {
                    const query = categoryInput.value.trim();
                    if (query === '' || categorySuggestions.classList.contains('hidden')) {
                        fetchAndDisplayCategorySuggestions(query);
                    }
                });

                categoryInput.addEventListener('keydown', (e) => {
                    const suggestions = categorySuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || categorySuggestions.classList.contains('hidden')) return;

                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex - 1 + suggestions.length) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter' && activeIndex >= 0) {
                        e.preventDefault();
                        suggestions[activeIndex].click();
                    } else if (e.key === 'Escape') {
                        categorySuggestions.innerHTML = '';
                        categorySuggestions.classList.add('hidden');
                    }
                });
                
                document.addEventListener('click', function(event) {
                    if (!categoryInput.contains(event.target) && !categorySuggestions.contains(event.target)) {
                        categorySuggestions.classList.add('hidden');
                    }
                });
            },

            initBrandAutocomplete: function() {
                const manufacturerInput = document.getElementById('input-manufacturer');
                const manufacturerIdInput = document.querySelector('input[name="manufacturer_id"]');
                const manufacturerSuggestions = document.getElementById('product-manufacturer-autocomplete');
                const selectedManufacturerContainer = document.getElementById('selected-manufacturer-container');
                const userToken = BackendModule.config.userToken;

                let brandDebounceTimer;
                let brandAbortController = null;

                function addBrandToList(name, id) {
                    if (!selectedManufacturerContainer) return;
                    // Ако вече има badge, не добавяй втори
                    if (selectedManufacturerContainer.querySelector('.selected-brand-badge')) return;
                    const brandElement = document.createElement('div');
                    brandElement.className = 'selected-brand-badge flex items-center justify-between bg-primary/10 text-primary px-3 py-1.5 rounded-md text-sm my-1';
                    brandElement.innerHTML = `
                        <span>${name}</span>
                        <button type="button" class="remove-selected-manufacturer ml-2 text-red-500 hover:text-red-700" data-id="${id}">
                            <i class="ri-close-line"></i>
                        </button>
                    `;
                    const removeButton = brandElement.querySelector('.remove-selected-manufacturer');
                    removeButton.addEventListener('click', function() {
                        manufacturerIdInput.value = '';
                        selectedManufacturerContainer.innerHTML = '';
                        manufacturerInput.value = '';
                        manufacturerInput.focus();
                    });
                    selectedManufacturerContainer.innerHTML = '';
                    selectedManufacturerContainer.appendChild(brandElement);
                    manufacturerSuggestions.classList.add('hidden');
                    manufacturerSuggestions.innerHTML = '';
                }

                // Handle pre-selected manufacturer if rendered by server
                if (manufacturerIdInput && manufacturerIdInput.value && selectedManufacturerContainer) {
                    const existingSelectedItem = selectedManufacturerContainer.querySelector('.selected-item');
                    if (existingSelectedItem) {
                        manufacturerInput.disabled = true;
                        const removeButton = existingSelectedItem.querySelector('.remove-selected-manufacturer');
                        if (removeButton) {
                           // Ensure event listener is attached if not already (e.g. if JS runs after DOM ready)
                           // This might be redundant if Twig adds its own listener, but safer to have one source of truth.
                           if (!removeButton.dataset.listenerAttached) {
                                removeButton.addEventListener('click', function() {
                                    manufacturerInput.disabled = false;
                                    manufacturerIdInput.value = '';
                                    selectedManufacturerContainer.innerHTML = '';
                                    manufacturerInput.value = '';
                                    manufacturerInput.focus();
                                });
                                removeButton.dataset.listenerAttached = 'true';
                           }
                        }
                    } else if (manufacturerInput.dataset.initialName) { // Fallback if not rendered but ID and name are known
                        addBrandToList(manufacturerInput.dataset.initialName, manufacturerIdInput.value);
                    }
                }

                function fetchAndDisplayBrandSuggestions(query) {
                    if (brandAbortController) {
                        brandAbortController.abort();
                    }
                    brandAbortController = new AbortController();
                    const signal = brandAbortController.signal;

                    manufacturerSuggestions.innerHTML = '<div class="autocomplete-loading p-2 text-sm text-gray-500">Зареждане...</div>';
                    manufacturerSuggestions.classList.remove('hidden');

                    const timestamp = new Date().getTime();
                    fetch(`index.php?route=catalog/product/autocomplete&type=manufacturer&filter_name=${encodeURIComponent(query)}&user_token=${userToken}&_=${timestamp}`, {
                        signal: signal,
                        headers: { 'Cache-Control': 'no-cache' }
                    })
                    .then(response => {
                        if (signal.aborted) return;
                        if (!response.ok) throw new Error('Грешка при зареждане на марки');
                        return response.json();
                    })
                    .then(data => {
                        if (signal.aborted) return;
                        manufacturerSuggestions.innerHTML = '';
                        if (data.length === 0) {
                            manufacturerSuggestions.innerHTML = '<div class="autocomplete-no-results p-2 text-sm text-gray-500">Няма намерени марки.</div>';
                            return;
                        }
                        const suggestionsContainer = document.createElement('div');
                        suggestionsContainer.className = 'autocomplete-suggestions max-h-60 overflow-y-auto';
                        data.forEach(brand => {
                            const item = document.createElement('div');
                            item.className = 'autocomplete-suggestion p-2 hover:bg-gray-100 cursor-pointer text-sm';
                            item.textContent = brand.name;
                            item.dataset.id = brand.id;
                            item.addEventListener('click', (e) => {
                                e.stopPropagation();
                                manufacturerIdInput.value = brand.id;
                                manufacturerInput.value = '';
                                manufacturerInput.dataset.lastSelectedName = brand.name;
                                addBrandToList(brand.name, brand.id);
                                manufacturerSuggestions.classList.add('hidden');
                            });
                            suggestionsContainer.appendChild(item);
                        });
                        manufacturerSuggestions.appendChild(suggestionsContainer);
                        manufacturerSuggestions.classList.remove('hidden');
                    })
                    .catch(error => {
                        if (signal.aborted) return;
                        console.error('Грешка при автодовършване на марки:', error);
                        manufacturerSuggestions.innerHTML = `<div class="autocomplete-error p-2 text-sm text-red-500">${error.message}</div>`;
                    });
                }

                manufacturerInput.addEventListener('input', () => {
                    if (manufacturerInput.disabled) return;
                    const query = manufacturerInput.value.trim();
                    clearTimeout(brandDebounceTimer);
                    brandDebounceTimer = setTimeout(() => {
                        fetchAndDisplayBrandSuggestions(query);
                    }, 500);
                });

                manufacturerInput.addEventListener('blur', () => {
                    setTimeout(function() {
                        manufacturerInput.value = '';
                    }, 200);
                });

                manufacturerInput.addEventListener('focus', () => {
                    if (manufacturerInput.disabled) return;
                    const query = manufacturerInput.value.trim();
                    if (query === '' || manufacturerSuggestions.classList.contains('hidden') || manufacturerSuggestions.innerHTML === '') {
                        fetchAndDisplayBrandSuggestions(query);
                    }
                });

                manufacturerInput.addEventListener('keydown', (e) => {
                    if (manufacturerInput.disabled) return;
                    const suggestions = manufacturerSuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || manufacturerSuggestions.classList.contains('hidden')) return;
                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));
                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex - 1 + suggestions.length) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter' && activeIndex >= 0) {
                        e.preventDefault();
                        suggestions[activeIndex].click();
                    } else if (e.key === 'Escape') {
                        manufacturerSuggestions.innerHTML = '';
                        manufacturerSuggestions.classList.add('hidden');
                    }
                });
                
                document.addEventListener('click', function(event) {
                    if (!manufacturerInput.contains(event.target) && !manufacturerSuggestions.contains(event.target)) {
                        manufacturerSuggestions.classList.add('hidden');
                    }
                });
            },

            /**
             * Инициализация на функционалността за характеристики
             */
            initSpecifications: function() {
                const addAttributeBtn = document.getElementById('add-attribute');
                const attributesContainer = document.getElementById('attributes-container');
                
                if (addAttributeBtn && attributesContainer) {
                    addAttributeBtn.addEventListener('click', () => {
                        const attributeIndex = document.querySelectorAll('.attribute-item').length;
                        const attributeHtml = `
                            <div class="attribute-item grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
                                    <input type="text" name="product_specification[${attributeIndex}][name]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
                                    <div class="flex space-x-2">
                                        <input type="text" name="product_specification[${attributeIndex}][value]" class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                                        <button type="button" class="remove-attribute px-3 py-2 bg-red-100 text-red-600 rounded hover:bg-red-200 transition-colors">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = attributeHtml.trim();
                        attributesContainer.appendChild(tempDiv.firstChild);
                    });
                    
                    // Премахване на характеристика
                    document.addEventListener('click', function(e) {
                        if (e.target.closest('.remove-attribute')) {
                            const attributeItem = e.target.closest('.attribute-item');
                            if (attributeItem && confirm('Сигурни ли сте, че искате да премахнете тази характеристика?')) {
                                attributeItem.remove();
                            }
                        }
                    });
                }
            },

            /**
             * Показване на съобщение
             * @param {string} type Тип на съобщението (success, error, info)
             * @param {string} message Съдържание на съобщението
             * @param {number} duration Продължителност в секунди (0 за постоянно съобщение)
             */
            showAlert: function(type, message, duration = 3) {
                // Премахване на съществуващи съобщения
                const existingAlert = document.querySelector('.alert-message');
                if (existingAlert) {
                    existingAlert.remove();
                }
                
                // Създаване на новото съобщение
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert-message fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                    type === 'success' ? 'bg-green-100 text-green-800 border-l-4 border-green-500' :
                    type === 'error' ? 'bg-red-100 text-red-800 border-l-4 border-red-500' :
                    'bg-blue-100 text-blue-800 border-l-4 border-blue-500'
                } flex items-center space-x-3`;
                
                // Икона според типа
                const iconClass = 
                    type === 'success' ? 'ri-check-line' :
                    type === 'error' ? 'ri-error-warning-line' :
                    'ri-information-line';
                
                alertDiv.innerHTML = `
                    <div class="flex-shrink-0">
                        <i class="${iconClass}"></i>
                    </div>
                    <div>${message}</div>
                    <button class="ml-auto text-gray-500 hover:text-gray-700" id="close-alert">
                        <i class="ri-close-line"></i>
                    </button>
                `;
                
                document.body.appendChild(alertDiv);
                
                // Бутон за затваряне
                const closeButton = alertDiv.querySelector('#close-alert');
                closeButton.addEventListener('click', () => alertDiv.remove());
                
                // Автоматично затваряне след определено време
                if (duration > 0) {
                    setTimeout(() => {
                        if (document.body.contains(alertDiv)) {
                            alertDiv.remove();
                        }
                    }, duration * 1000);
                }
            }
        });

        // Извикване на основната инициализираща функция
        BackendModule.initProductForm();
    } // Край на if (typeof BackendModule !== 'undefined')

})();
