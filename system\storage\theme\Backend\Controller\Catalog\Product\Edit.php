<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Edit extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }
    
    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
        
        // Зареждаме само product-form.js, тъй като backend.js вече се зарежда глобално
        $productFormUrl = $base . 'backend_js/product-form.js';
        $productFormPath = DIR_THEME . 'Backend/View/Javascript/product-form.js';
        
        if (file_exists($productFormPath)) {
            $lastModified = filemtime($productFormPath);
            $productFormUrl .= '?v=' . $lastModified;
            $this->document->addScript($productFormUrl, 'footer');
        }
    }

    public function searchCategories() {
        $json = [];
        
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel'
        ]);
        
        if (isset($this->request->get['filter_name'])) {
            $filter_data = [
                'filter_name' => $this->request->get['filter_name'],
                'sort'        => 'name',
                'order'       => 'ASC',
                'start'       => 0,
                'limit'       => 10
            ];
            
            $results = $this->categoryModel->getCategories($filter_data);
            
            foreach ($results as $result) {
                $json[] = [
                    'id'   => $result['category_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
                ];
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function searchManufacturers() {
        $json = [];
        
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel'
        ]);
        
        if (isset($this->request->get['filter_name'])) {
            $filter_data = [
                'filter_name' => $this->request->get['filter_name'],
                'start'       => 0,
                'limit'       => 10
            ];
            
            $results = $this->manufacturerModel->getManufacturers($filter_data);
            
            foreach ($results as $result) {
                $json[] = [
                    'id'   => $result['manufacturer_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
                ];
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function prepareProductForm() {

        $this->loadLanguage('catalog/product');

        $this->initAdminData();

        // Зареждане на модели
        $this->loadModelsAs([
            'catalog/product'       => 'productModel',
            'catalog/category'      => 'categoryModel',
            'catalog/manufacturer'  => 'manufacturerModel',
            'localisation/language' => 'languageModel',
            'setting/store'         => 'storeModel',
            'tool/image'            => 'imageModel' // За placeholder изображение
        ]);

        $product_id = $this->requestGet('product_id', 0);
        $product_info = [];

        if ($product_id && $this->request->server['REQUEST_METHOD'] != 'POST') {
            $product_info = $this->productModel->getProduct($product_id);
        }

        if ($product_id) {
            $this->setTitle('Редактиране на продукт');
            $this->setData('action', $this->getAdminLink('catalog/product/edit', 'product_id=' . $product_id, true));
        }

        $this->setData('site_url', HTTPS_CATALOG);
        
        // Данни за продукта
        $this->setData('product_id', $product_id);
        $this->setBackUrl();
        
        // Задаване на пътя до контролера за използване в JavaScript
        $this->setData('catalog_controller', $this->getAdminLink('catalog/product/edit', '', true));

        // Езици
        $languages = $this->languageModel->getLanguages();
        $this->setData('languages', $languages);

        // Стойности по подразбиране за полетата
        $default_values = [
            'model'             => '',
            'sku'               => '',
            'upc'               => '',
            'ean'               => '',
            'jan'               => '',
            'isbn'              => '',
            'mpn'               => '',
            'location'          => '',
            'price'             => '0.00',
            'tax_class_id'      => '0',
            'quantity'          => 1,
            'minimum'           => 1,
            'subtract'          => 1,
            'stock_status_id'   => $this->getConfig('config_stock_status_id'),
            'shipping'          => 1,
            'date_available'    => date('Y-m-d'),
            'length'            => '0.00',
            'width'             => '0.00',
            'height'            => '0.00',
            'length_class_id'   => $this->getConfig('config_length_class_id'),
            'weight'            => '0.00',
            'weight_class_id'   => $this->getConfig('config_weight_class_id'),
            'status'            => 1, // Правилна стойност за статус (1 = активен, 0 = неактивен)
            'sort_order'        => 1,
            'manufacturer_id'   => 0,
            'image'             => '',
            'main_category_id'  => 0
        ];
        
        // Обединяване на стойностите по подразбиране с данните от продукта (ако има такъв)
        $product_data = !empty($product_info) ? array_merge($default_values, $product_info) : $default_values;
        
        // Задаване на данните, като приоритет имат POST стойностите
        foreach ($product_data as $field => $value) {
            $this->setData($field, $this->requestPost($field, $value));
        }

        // Product Description (многоезични полета)
        $product_description_post = $this->requestPost('product_description');
        if ($product_description_post !== null) {
            $this->setData('product_description', $product_description_post);
        } elseif ($product_id) {
            $this->setData('product_description', $this->productModel->getProductDescriptions($product_id));
        } else {
            $product_description = [];
            foreach ($languages as $language) {
                $product_description[$language['language_id']] = [
                    'name'             => '',
                    'description'      => '',
                    'tag'              => '',
                    'meta_title'       => '',
                    'meta_description' => '',
                    'meta_keyword'     => ''
                ];
            }
            $this->setData('product_description', $product_description);
        }

        // Placeholder за изображение
        $this->setData('placeholder', $this->imageModel->resize('no_image.png', 192, 192));

        // Основно изображение на продукта
        $image_post = $this->requestPost('image');
        if ($image_post !== null && is_file(DIR_IMAGE . $image_post)) {
            $this->setData('thumb', $this->imageModel->resize($image_post, 192, 192));
        } elseif (!empty($product_info) && !empty($product_info['image'])) {

            $this->load->model('tool/Imageservice');
            $image_details = $this->model_tool_Imageservice->getImageDetailsByPath($product_info['image'], 192, 192);

            $this->setData('thumb', $image_details['resized_image_url']);
        } else {
            $this->setData('thumb', $this->imageModel->resize('no_image.png', 192, 192));
        }

        // Категории
        $categories_data = $this->categoryModel->getCategories(['sort' => 'name', 'order' => 'ASC']);
        $categories_flat = $this->getAllCategoriesFlat($categories_data);
        $this->setData('categories', $categories_flat);

        $product_categories = $this->requestPost('product_category');
        if ($product_categories !== null) {
        } elseif ($product_id) {
            $product_categories = $this->productModel->getProductCategories($product_id);
        } else {
            $product_categories = [];
        }
        $this->setData('product_category', $product_categories);

 
        // Основна категория (ако се използва)
        $main_category_id = $this->requestPost('main_category_id');
        if ($main_category_id !== null) {
            $this->setData('main_category_id', $main_category_id);
        } elseif (isset($product_info['main_category_id'])) {
            $this->setData('main_category_id', $product_info['main_category_id']);
        } else {
             $this->setData('main_category_id', 0);
        }

        // Производители
        $manufacturer_id = $this->requestPost('manufacturer_id');
        if ($manufacturer_id !== null) {
            $this->setData('manufacturer_id', $manufacturer_id);
        } elseif (!empty($product_info)) {
            $this->setData('manufacturer_id', $product_info['manufacturer_id']);
        } else {
            $this->setData('manufacturer_id', 0);
        }
        $this->setData('manufacturers', $this->manufacturerModel->getManufacturers(['sort' => 'name']));

        // Подготвяне на данните за бранд бадж-а
        $manufacturer_name = '';
        if($this->data['manufacturer_id']) {
            $manufacturer_name = $this->getManufacturerName($this->data['manufacturer_id']);
        }
        $this->setData('manufacturer_name', $manufacturer_name);

        // Промоционални цени
        $product_specials_post = $this->requestPost('product_special');
        if ($product_specials_post !== null) {
            $this->setData('product_special', $product_specials_post);
        } elseif ($product_id) {
            $this->setData('product_special', $this->productModel->getProductSpecials($product_id));
        } else {
            $this->setData('product_special', []);
        }

        // Изображения на продукта
        $product_images_data = $this->requestPost('product_image');
        if ($product_images_data !== null) {
        } elseif ($product_id) {
            $product_images_data = $this->productModel->getProductImages($product_id);
        } else {
            $product_images_data = [];
        }

        $product_images = [];
        foreach ($product_images_data as $product_image_item) {

             if (isset($product_image_item['image'])) {
                $image = $product_image_item['image'];
                $thumb = $this->model_tool_Imageservice->getImageDetailsByPath($product_image_item['image'], 192, 192);
            } else {
                $image = '';
                $thumb = $this->imageModel->resize('no_image.png', 192, 192);
            }
            $product_images[] = [
                'image'      => $image,
                'thumb'      => $thumb,
                'sort_order' => isset($product_image_item['sort_order']) ? $product_image_item['sort_order'] : ''
            ];
        }
        $this->setData('product_images', $product_images);

        // Атрибути на продукта
        $product_attributes_post = $this->requestPost('product_attribute');
        if ($product_attributes_post !== null) {
            $this->setData('product_attribute', $product_attributes_post);
        } elseif ($product_id) {
            $this->setData('product_attribute', $this->productModel->getProductAttributes($product_id));
        } else {
            $this->setData('product_attribute', []);
        }

        // SEO URL данни
        $product_seo_url_post = $this->requestPost('product_seo_url');
        if ($product_seo_url_post !== null) {
            $this->setData('product_seo_url', $product_seo_url_post);
        } elseif ($product_id) {
            $this->setData('product_seo_url', $this->productModel->getProductSeoUrls($product_id));
        } else {
            $this->setData('product_seo_url', []);
        }

        // Свързани продукти
        $product_related_post = $this->requestPost('product_related');
        if ($product_related_post !== null) {
            $this->setData('product_related', $product_related_post);
        } elseif ($product_id) {
            $this->setData('product_related', $this->productModel->getProductRelated($product_id));
        } else {
            $this->setData('product_related', []);
        }

        // Зареждане на моделите с по-кратки алиаси
        $this->loadModelsAs([
            'localisation/stock_status' => 'stockStatusModel',
            'localisation/tax_class' => 'taxClassModel',
            'localisation/length_class' => 'lengthClassModel',
            'localisation/weight_class' => 'weightClassModel'
        ]);

        // Задаване на всички данни наведнъж
        $this->setData([
            'stock_statuses' => $this->stockStatusModel->getStockStatuses(),
            'tax_classes' => $this->taxClassModel->getTaxClasses(),
            'length_classes' => $this->lengthClassModel->getLengthClasses(),
            'weight_classes' => $this->weightClassModel->getWeightClasses(),
            'link_filemanager' => $this->getAdminLink('common/filemanager', '', true) . '&field='
        ]);

    }

    private function setBackUrl() {
         $this->setData([
            'back_url' => $this->getAdminLink('catalog/product', '', true, ['product_id'])
        ]);
    }

    /**
     * Рекурсивно извличане на всички категории за плосък списък, подходящ за select
     */
    private function getAllCategoriesFlat($categories, $parent_id = 0, $parent_name = '') {
        $output = [];
        $category_data = [];
        foreach ($categories as $category) {
            $category_data[$category['parent_id']][] = $category;
        }
        return $this->buildCategoryFlatList($category_data, 0, '');
    }

    private function buildCategoryFlatList($categories_data, $parent_id = 0, $path = '') {
        $output = [];
        if (isset($categories_data[$parent_id])) {
            foreach ($categories_data[$parent_id] as $category) {
                $current_path = $path . $category['name'];
                $output[$category['category_id']] = [
                    'category_id' => $category['category_id'],
                    'name'        => $current_path
                ];
                $children = $this->buildCategoryFlatList($categories_data, $category['category_id'], $current_path . ' &gt; ');
                foreach ($children as $category_id => $child) {
                    $output[$category_id] = $child;
                }
            }
        }
        return $output;
    }

    private function getManufacturerName($manufacturer_id) {
        foreach ($this->data['manufacturers'] as $manufacturer) {
            if ($manufacturer['manufacturer_id'] == $manufacturer_id) {
                return $manufacturer['name'];
            }
        }
        return '';
    }
    
    /**
     * Запазване на продукт
     */
    public function save() {
        $json = [];
        
        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }
        
        // Валидация на задължителни полета
        if (!isset($this->request->post['product_description']) || !is_array($this->request->post['product_description'])) {
            $json['error'] = 'Липсва описание на продукта';
            $this->setJSONResponseOutput($json);
            return;
        }
        
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/product' => 'productModel',
            'tool/image' => 'imageModel'
        ]);
        
        // Подготвяне на данните за продукта
        $data = [
            'model' => $this->request->post['model'] ?? '',
            'sku' => $this->request->post['sku'] ?? '',
            'upc' => $this->request->post['upc'] ?? '',
            'ean' => $this->request->post['ean'] ?? '',
            'jan' => $this->request->post['jan'] ?? '',
            'isbn' => $this->request->post['isbn'] ?? '',
            'mpn' => $this->request->post['mpn'] ?? '',
            'location' => $this->request->post['location'] ?? '',
            'price' => $this->request->post['price'] ?? 0,
            'tax_class_id' => $this->request->post['tax_class_id'] ?? 0,
            'quantity' => $this->request->post['quantity'] ?? 0,
            'minimum' => $this->request->post['minimum'] ?? 1,
            'subtract' => isset($this->request->post['subtract']) ? 1 : 0,
            'stock_status_id' => $this->request->post['stock_status_id'] ?? 0,
            'shipping' => isset($this->request->post['shipping']) ? 1 : 0,
            'date_available' => $this->request->post['date_available'] ?? date('Y-m-d'),
            'length' => $this->request->post['length'] ?? 0,
            'width' => $this->request->post['width'] ?? 0,
            'height' => $this->request->post['height'] ?? 0,
            'length_class_id' => $this->request->post['length_class_id'] ?? 0,
            'weight' => $this->request->post['weight'] ?? 0,
            'weight_class_id' => $this->request->post['weight_class_id'] ?? 0,
            'status' => isset($this->request->post['status']) ? 1 : 0,
            'sort_order' => $this->request->post['sort_order'] ?? 0,
            'manufacturer_id' => $this->request->post['manufacturer_id'] ?? 0,
            'product_description' => $this->request->post['product_description'],
            'product_category' => $this->request->post['product_category'] ?? [],
            'product_image' => $this->request->post['product_image'] ?? [],
            'product_related' => $this->request->post['product_related'] ?? [],
            'product_special' => $this->request->post['product_special'] ?? [],
            'product_attribute' => $this->request->post['product_attribute'] ?? [],
            'product_seo_url' => $this->request->post['product_seo_url'] ?? []
        ];
        
        // Обработка на основното изображение
        if (!empty($this->request->post['image'])) {
            $data['image'] = $this->request->post['image'];
        } else {
            $data['image'] = '';
        }
        
        // Обработка на допълнителните изображения
        $product_images = [];
        if (!empty($this->request->post['product_image'])) {
            $sort_order = 0;
            foreach ($this->request->post['product_image'] as $key => $image) {
                if (is_file(DIR_IMAGE . $image)) {
                    $product_images[] = [
                        'image' => $image,
                        'sort_order' => isset($this->request->post['product_image_sort_order'][$key]) ? (int)$this->request->post['product_image_sort_order'][$key] : $sort_order++
                    ];
                }
            }
        }
        $data['product_image'] = $product_images;
        
        // Запазване на продукта
        $product_id = $this->request->get['product_id'] ?? 0;

        F()->log->developer($data, __FILE__, __LINE__);
        
        // if ($product_id) {
        //     $this->productModel->editProduct($product_id, $data);
        //     $json['success'] = 'Продуктът беше успешно актуализиран';
        // } else {
        //     $product_id = $this->productModel->addProduct($data);
        //     $json['success'] = 'Продуктът беше успешно добавен';
        // }

        $json['success'] = 'Продуктът беше успешно актуализиран';
        
        // Добавяне на ID на продукта в отговора
        $json['product_id'] = $product_id;
        
        // Добавяне на URL за пренасочване
        $json['redirect'] = $this->getAdminLink('catalog/product/edit', 'product_id=' . $product_id, true);
        
        $this->setJSONResponseOutput($json);
    }
}